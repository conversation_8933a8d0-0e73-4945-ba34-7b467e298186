
.list-table-format{
    display: grid;
    grid-template-columns: 0.5fr 2fr 1fr 1fr 0.5fr;
    align-items: center;
    padding: 12px 24px;
    border: 1px solid #cacaca;

}

.list-table-format.title{
    background-color: #f9f9f9;

}

.list-table-format img{
    width: 50px;
}

.list-table-format img,.list-table-format p,.title p{
    text-align: center;
}

@media(max-wise: 768px){
    .list-table-format{
        grid-template-columns: 1fr 3fr 1fr;
    }
    .list-table-format.title{
        display: none;
    }
}