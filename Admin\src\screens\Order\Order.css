/* Order.css */
.order.add {
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
  }
  
  h3 {
    text-align: center;
    margin-bottom: 20px;
    font-size: 24px;
    color: #333;
  }
  
  .order-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  
  .order-item {
    background-color: #ffffff;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    display: flex;
    gap: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    align-items: flex-start;
  }
  
  .order-item img {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 5px;
  }
  
  .order-item > div {
    flex-grow: 1;
  }
  
  .order-address-details,
  .order-food-details {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  
  .order-item-food {
    font-size: 16px;
    font-weight: bold;
    color: #555;
    margin-bottom: 10px;
  }
  
  .order-item-name {
    font-size: 14px;
    color: #777;
  }
  
  .order-item-address p {
    font-size: 14px;
    color: #777;
    margin: 0;
  }
  
  .order-item-phone {
    font-size: 14px;
    font-weight: bold;
    color: #555;
    margin-top: 10px;
  }
  
  .order-item > p {
    font-size: 14px;
    color: #555;
  }
  
  .amt-len {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-end;
  }
  
  .amt-len p {
    font-size: 14px;
    color: #555;
    margin-bottom: 8px;
  }
  
  select {
    padding: 6px 10px;
    margin-top: 5px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: #fff;
    font-size: 14px;
    color: #555;
    cursor: pointer;
    width: 180px;
  }
  
  select:focus {
    outline: none;
    border-color: #007bff;
  }
  
  select option {
    padding: 8px;
    font-size: 14px;
  }
  