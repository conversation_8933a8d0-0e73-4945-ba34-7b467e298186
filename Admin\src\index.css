@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap');
:root {
  font-family: "Montserrat", serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(0, 0, 0, 0.87);
  background-color: #ffffff;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

*{
  margin:0;
  padding: 0;
  font-weight: 600;
  box-sizing: border-box;
  scroll-behavior: smooth;
}

body{
  height: 100vh;
}

a{
  text-decoration: none;
  color: inherit;
}

.app{
  width:90%;
  margin:auto;
}
hr{
  width: 100%;
    border: none;
    height: 1px;
    background-color: #ccc;
}
.app-component{
  display: flex;
}

.app-content{
  display: flex;
}
.flex-col{
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.cursor{
  cursor:pointer
}


@keyframes fadeIn{
  0%{
    opacity: 0;
  }
  100%{
    opacity: 100;
  }
}